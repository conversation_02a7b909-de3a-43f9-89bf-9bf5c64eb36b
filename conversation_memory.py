"""
Conversation Memory System for TeleConnect Customer Support RAG Bot
Handles conversation history, context management, and session persistence
"""

import os
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from dataclasses import dataclass, asdict
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ConversationMessage:
    """Data class for individual conversation messages"""
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ConversationSession:
    """Data class for conversation sessions"""
    session_id: str
    user_id: Optional[str]
    created_at: str
    last_updated: str
    messages: List[ConversationMessage]
    context_summary: Optional[str] = None
    customer_info: Optional[Dict[str, Any]] = None

class TelecomConversationMemory:
    """
    Conversation memory system for maintaining context across interactions
    """
    
    def __init__(self, storage_path: str = "./conversations", max_history: int = 10):
        """
        Initialize conversation memory system
        
        Args:
            storage_path: Directory to store conversation files
            max_history: Maximum number of messages to keep in active memory
        """
        self.storage_path = Path(storage_path)
        self.max_history = max_history
        self.active_sessions: Dict[str, ConversationSession] = {}
        
        # Create storage directory if it doesn't exist
        self.storage_path.mkdir(exist_ok=True)
        
        logger.info(f"Initialized conversation memory with storage at: {storage_path}")
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """
        Create a new conversation session
        
        Args:
            user_id: Optional user identifier
            
        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()
        
        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            created_at=current_time,
            last_updated=current_time,
            messages=[],
            context_summary=None,
            customer_info={}
        )
        
        self.active_sessions[session_id] = session
        logger.info(f"Created new conversation session: {session_id}")
        
        return session_id
    
    def add_message(self, session_id: str, role: str, content: str, 
                   metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a message to the conversation
        
        Args:
            session_id: Session identifier
            role: Message role ('user' or 'assistant')
            content: Message content
            metadata: Optional metadata for the message
        """
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found, creating new session")
            self.create_session()
            if session_id not in self.active_sessions:
                session_id = self.create_session()
        
        session = self.active_sessions[session_id]
        
        message = ConversationMessage(
            role=role,
            content=content,
            timestamp=datetime.now().isoformat(),
            metadata=metadata or {}
        )
        
        session.messages.append(message)
        session.last_updated = datetime.now().isoformat()
        
        # Trim messages if exceeding max history
        if len(session.messages) > self.max_history * 2:  # *2 for user+assistant pairs
            # Keep the most recent messages
            session.messages = session.messages[-self.max_history * 2:]
            logger.info(f"Trimmed conversation history for session {session_id}")
        
        # Save session to disk
        self._save_session(session)
        
        logger.debug(f"Added {role} message to session {session_id}")
    
    def get_conversation_history(self, session_id: str, 
                               limit: Optional[int] = None) -> List[Dict[str, str]]:
        """
        Get conversation history for a session
        
        Args:
            session_id: Session identifier
            limit: Optional limit on number of messages to return
            
        Returns:
            List of message dictionaries
        """
        if session_id not in self.active_sessions:
            # Try to load from disk
            session = self._load_session(session_id)
            if session:
                self.active_sessions[session_id] = session
            else:
                logger.warning(f"Session {session_id} not found")
                return []
        
        session = self.active_sessions[session_id]
        messages = session.messages
        
        if limit:
            messages = messages[-limit:]
        
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp
            }
            for msg in messages
        ]
    
    def update_customer_info(self, session_id: str, customer_info: Dict[str, Any]) -> None:
        """
        Update customer information for a session
        
        Args:
            session_id: Session identifier
            customer_info: Customer information dictionary
        """
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            if session.customer_info is None:
                session.customer_info = {}
            session.customer_info.update(customer_info)
            session.last_updated = datetime.now().isoformat()
            self._save_session(session)
            logger.info(f"Updated customer info for session {session_id}")
    
    def get_customer_info(self, session_id: str) -> Dict[str, Any]:
        """
        Get customer information for a session
        
        Args:
            session_id: Session identifier
            
        Returns:
            Customer information dictionary
        """
        if session_id in self.active_sessions:
            return self.active_sessions[session_id].customer_info or {}
        
        # Try to load from disk
        session = self._load_session(session_id)
        if session:
            self.active_sessions[session_id] = session
            return session.customer_info or {}
        
        return {}
    
    def generate_context_summary(self, session_id: str) -> str:
        """
        Generate a summary of the conversation context
        
        Args:
            session_id: Session identifier
            
        Returns:
            Context summary string
        """
        history = self.get_conversation_history(session_id)
        
        if not history:
            return "No previous conversation history."
        
        # Extract key topics and issues from conversation
        topics = set()
        issues = []
        
        for msg in history:
            content_lower = msg["content"].lower()
            
            # Identify topics
            if any(word in content_lower for word in ['internet', 'wifi', 'connection']):
                topics.add('Internet/WiFi')
            if any(word in content_lower for word in ['mobile', 'phone', 'signal']):
                topics.add('Mobile Service')
            if any(word in content_lower for word in ['bill', 'charge', 'payment']):
                topics.add('Billing')
            if any(word in content_lower for word in ['plan', 'upgrade', 'change']):
                topics.add('Service Plans')
            
            # Identify issues (from user messages)
            if msg["role"] == "user" and any(word in content_lower for word in ['problem', 'issue', 'not working', 'slow']):
                issues.append(msg["content"][:100])  # First 100 chars
        
        summary_parts = []
        
        if topics:
            summary_parts.append(f"Topics discussed: {', '.join(topics)}")
        
        if issues:
            summary_parts.append(f"Issues mentioned: {'; '.join(issues[:3])}")  # Max 3 issues
        
        customer_info = self.get_customer_info(session_id)
        if customer_info:
            info_summary = []
            for key, value in customer_info.items():
                if key in ['account_number', 'plan_type', 'service_area']:
                    info_summary.append(f"{key}: {value}")
            if info_summary:
                summary_parts.append(f"Customer info: {', '.join(info_summary)}")
        
        return "; ".join(summary_parts) if summary_parts else "General customer support conversation."
    
    def cleanup_old_sessions(self, days_old: int = 30) -> int:
        """
        Clean up old conversation sessions
        
        Args:
            days_old: Number of days after which to delete sessions
            
        Returns:
            Number of sessions cleaned up
        """
        cutoff_date = datetime.now() - timedelta(days=days_old)
        cleaned_count = 0
        
        # Clean up active sessions
        sessions_to_remove = []
        for session_id, session in self.active_sessions.items():
            last_updated = datetime.fromisoformat(session.last_updated)
            if last_updated < cutoff_date:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
            cleaned_count += 1
        
        # Clean up stored session files
        for file_path in self.storage_path.glob("*.json"):
            try:
                with open(file_path, 'r') as f:
                    session_data = json.load(f)
                
                last_updated = datetime.fromisoformat(session_data.get('last_updated', ''))
                if last_updated < cutoff_date:
                    file_path.unlink()
                    cleaned_count += 1
                    
            except Exception as e:
                logger.error(f"Error processing session file {file_path}: {str(e)}")
        
        logger.info(f"Cleaned up {cleaned_count} old conversation sessions")
        return cleaned_count
    
    def _save_session(self, session: ConversationSession) -> None:
        """Save session to disk"""
        try:
            file_path = self.storage_path / f"{session.session_id}.json"
            
            # Convert to dictionary for JSON serialization
            session_dict = asdict(session)
            
            with open(file_path, 'w') as f:
                json.dump(session_dict, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving session {session.session_id}: {str(e)}")
    
    def _load_session(self, session_id: str) -> Optional[ConversationSession]:
        """Load session from disk"""
        try:
            file_path = self.storage_path / f"{session_id}.json"
            
            if not file_path.exists():
                return None
            
            with open(file_path, 'r') as f:
                session_dict = json.load(f)
            
            # Convert messages back to ConversationMessage objects
            messages = [
                ConversationMessage(**msg) for msg in session_dict.get('messages', [])
            ]
            
            session_dict['messages'] = messages
            return ConversationSession(**session_dict)
            
        except Exception as e:
            logger.error(f"Error loading session {session_id}: {str(e)}")
            return None
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics about conversation sessions"""
        active_count = len(self.active_sessions)
        
        # Count stored sessions
        stored_count = len(list(self.storage_path.glob("*.json")))
        
        # Calculate total messages in active sessions
        total_messages = sum(len(session.messages) for session in self.active_sessions.values())
        
        return {
            "active_sessions": active_count,
            "stored_sessions": stored_count,
            "total_messages_in_active": total_messages,
            "storage_path": str(self.storage_path)
        }

# Example usage and testing
if __name__ == "__main__":
    # Initialize conversation memory
    memory = TelecomConversationMemory()
    
    # Create a test session
    session_id = memory.create_session(user_id="test_user")
    print(f"Created session: {session_id}")
    
    # Add some test messages
    memory.add_message(session_id, "user", "My internet is very slow")
    memory.add_message(session_id, "assistant", "I can help you troubleshoot your internet connection. Let me check a few things first.")
    memory.add_message(session_id, "user", "I'm on the premium plan")
    
    # Update customer info
    memory.update_customer_info(session_id, {
        "plan_type": "Premium Internet",
        "account_number": "12345"
    })
    
    # Get conversation history
    history = memory.get_conversation_history(session_id)
    print(f"\nConversation history ({len(history)} messages):")
    for msg in history:
        print(f"{msg['role']}: {msg['content']}")
    
    # Generate context summary
    summary = memory.generate_context_summary(session_id)
    print(f"\nContext summary: {summary}")
    
    # Get session statistics
    stats = memory.get_session_stats()
    print(f"\nSession statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print(f"\nConversation memory system test completed successfully!")
