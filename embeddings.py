"""
OpenAI Embeddings Implementation for TeleConnect Customer Support RAG Bot
Handles text embedding generation using OpenAI's embedding models
"""

import os
import openai
from typing import List, Dict, Any, Optional
from langchain.docstore.document import Document
import logging
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelecomEmbeddingService:
    """
    OpenAI embedding service for generating embeddings from text
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "text-embedding-3-small"):
        """
        Initialize OpenAI embedding service
        
        Args:
            api_key: OpenAI API key (if not provided, will use environment variable)
            model: OpenAI embedding model to use
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model = model
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
        
        # Initialize OpenAI client
        openai.api_key = self.api_key
        self.client = openai.OpenAI(api_key=self.api_key)
        
        logger.info(f"Initialized OpenAI embedding service with model: {model}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text
        
        Args:
            text: Input text to embed
            
        Returns:
            Embedding vector as list of floats
        """
        try:
            # Clean and prepare text
            text = text.replace("\n", " ").strip()
            
            if not text:
                raise ValueError("Text cannot be empty")
            
            # Generate embedding
            response = self.client.embeddings.create(
                input=text,
                model=self.model
            )
            
            embedding = response.data[0].embedding
            logger.debug(f"Generated embedding for text of length {len(text)}")
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise
    
    def generate_embeddings_batch(self, texts: List[str], batch_size: int = 100) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches
        
        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each batch
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        embeddings = []
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} texts)")
            
            try:
                # Clean texts
                cleaned_batch = [text.replace("\n", " ").strip() for text in batch if text.strip()]
                
                if not cleaned_batch:
                    logger.warning(f"Batch {batch_num} contains no valid texts")
                    continue
                
                # Generate embeddings for batch
                response = self.client.embeddings.create(
                    input=cleaned_batch,
                    model=self.model
                )
                
                # Extract embeddings
                batch_embeddings = [data.embedding for data in response.data]
                embeddings.extend(batch_embeddings)
                
                # Add small delay to avoid rate limiting
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {str(e)}")
                # Add empty embeddings for failed batch to maintain alignment
                embeddings.extend([[] for _ in batch])
                continue
        
        logger.info(f"Generated {len(embeddings)} embeddings from {len(texts)} texts")
        return embeddings
    
    def embed_documents(self, documents: List[Document]) -> List[List[float]]:
        """
        Generate embeddings for a list of Document objects
        
        Args:
            documents: List of Document objects
            
        Returns:
            List of embedding vectors corresponding to documents
        """
        if not documents:
            return []
        
        # Extract text content from documents
        texts = [doc.page_content for doc in documents]
        
        logger.info(f"Generating embeddings for {len(documents)} documents")
        
        # Generate embeddings
        embeddings = self.generate_embeddings_batch(texts)
        
        # Validate results
        valid_embeddings = []
        for i, embedding in enumerate(embeddings):
            if embedding:  # Non-empty embedding
                valid_embeddings.append(embedding)
            else:
                logger.warning(f"Empty embedding for document {i}, using zero vector")
                # Create a zero vector of appropriate dimension (1536 for text-embedding-3-small)
                valid_embeddings.append([0.0] * 1536)
        
        return valid_embeddings
    
    def embed_query(self, query: str) -> List[float]:
        """
        Generate embedding for a user query
        
        Args:
            query: User query text
            
        Returns:
            Query embedding vector
        """
        logger.info(f"Generating embedding for query: {query[:100]}...")
        return self.generate_embedding(query)
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of embeddings for the current model
        
        Returns:
            Embedding dimension
        """
        # Known dimensions for OpenAI models
        model_dimensions = {
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "text-embedding-ada-002": 1536
        }
        
        return model_dimensions.get(self.model, 1536)
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score (-1 to 1)
        """
        try:
            import numpy as np
            
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except ImportError:
            logger.warning("NumPy not available, using basic similarity calculation")
            # Basic dot product similarity (not normalized)
            return sum(a * b for a, b in zip(embedding1, embedding2))
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0
    
    def test_connection(self) -> bool:
        """
        Test the OpenAI API connection
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            test_embedding = self.generate_embedding("test connection")
            logger.info("OpenAI API connection test successful")
            return len(test_embedding) > 0
        except Exception as e:
            logger.error(f"OpenAI API connection test failed: {str(e)}")
            return False

# Example usage and testing
if __name__ == "__main__":
    try:
        # Initialize embedding service
        embedding_service = TelecomEmbeddingService()
        
        # Test connection
        if not embedding_service.test_connection():
            print("Failed to connect to OpenAI API. Please check your API key.")
            exit(1)
        
        print(f"OpenAI Embedding Service initialized successfully")
        print(f"Model: {embedding_service.model}")
        print(f"Embedding dimension: {embedding_service.get_embedding_dimension()}")
        
        # Test single embedding
        test_text = "How do I fix my internet connection?"
        embedding = embedding_service.generate_embedding(test_text)
        print(f"\nGenerated embedding for test query:")
        print(f"Text: {test_text}")
        print(f"Embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
        
        # Test batch embeddings
        test_texts = [
            "Internet connection is slow",
            "Mobile phone has no signal",
            "Billing question about charges"
        ]
        
        batch_embeddings = embedding_service.generate_embeddings_batch(test_texts)
        print(f"\nGenerated {len(batch_embeddings)} embeddings in batch")
        
        # Test similarity
        if len(batch_embeddings) >= 2:
            similarity = embedding_service.calculate_similarity(
                batch_embeddings[0], batch_embeddings[1]
            )
            print(f"Similarity between first two embeddings: {similarity:.4f}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        print("Make sure to set your OPENAI_API_KEY in the .env file")
