"""
Gemini Embeddings Implementation for TeleConnect Customer Support RAG Bot
Handles text embedding generation using Google's Gemini embedding models
"""

import os
import google.generativeai as genai
from typing import List, Dict, Any, Optional
from langchain.docstore.document import Document
import logging
import time
from dotenv import load_dotenv
import numpy as np

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelecomEmbeddingService:
    """
    Gemini embedding service for generating embeddings from text
    """

    def __init__(self, api_key: Optional[str] = None, model: str = "models/embedding-001"):
        """
        Initialize Gemini embedding service

        Args:
            api_key: Google API key (if not provided, will use environment variable)
            model: Gemini embedding model to use
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        self.model = model

        if not self.api_key:
            raise ValueError("Google API key is required. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")

        # Configure Gemini AI
        genai.configure(api_key=self.api_key)

        logger.info(f"Initialized Gemini embedding service with model: {model}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text using Gemini

        Args:
            text: Input text to embed

        Returns:
            Embedding vector as list of floats
        """
        try:
            # Clean and prepare text
            text = text.replace("\n", " ").strip()

            if not text:
                raise ValueError("Text cannot be empty")

            # Generate embedding using Gemini
            result = genai.embed_content(
                model=self.model,
                content=text,
                task_type="retrieval_document"
            )

            embedding = result['embedding']
            logger.debug(f"Generated embedding for text of length {len(text)}")

            return embedding

        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise
    
    def generate_embeddings_batch(self, texts: List[str], batch_size: int = 10) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches using Gemini

        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each batch (smaller for Gemini)

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        embeddings = []

        # Process texts one by one for Gemini (more reliable)
        for i, text in enumerate(texts):
            logger.info(f"Processing text {i+1}/{len(texts)}")

            try:
                # Clean text
                cleaned_text = text.replace("\n", " ").strip()

                if not cleaned_text:
                    logger.warning(f"Text {i+1} is empty, using zero vector")
                    embeddings.append([0.0] * 768)  # Default dimension for Gemini embeddings
                    continue

                # Generate embedding for single text
                result = genai.embed_content(
                    model=self.model,
                    content=cleaned_text,
                    task_type="retrieval_document"
                )

                embedding = result['embedding']
                embeddings.append(embedding)

                # Add small delay to avoid rate limiting
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error processing text {i+1}: {str(e)}")
                # Add zero vector for failed text
                embeddings.append([0.0] * 768)
                continue

        logger.info(f"Generated {len(embeddings)} embeddings from {len(texts)} texts")
        return embeddings
    
    def embed_documents(self, documents: List[Document]) -> List[List[float]]:
        """
        Generate embeddings for a list of Document objects
        
        Args:
            documents: List of Document objects
            
        Returns:
            List of embedding vectors corresponding to documents
        """
        if not documents:
            return []
        
        # Extract text content from documents
        texts = [doc.page_content for doc in documents]
        
        logger.info(f"Generating embeddings for {len(documents)} documents")
        
        # Generate embeddings
        embeddings = self.generate_embeddings_batch(texts)
        
        # Validate results
        valid_embeddings = []
        for i, embedding in enumerate(embeddings):
            if embedding:  # Non-empty embedding
                valid_embeddings.append(embedding)
            else:
                logger.warning(f"Empty embedding for document {i}, using zero vector")
                # Create a zero vector of appropriate dimension (1536 for text-embedding-3-small)
                valid_embeddings.append([0.0] * 1536)
        
        return valid_embeddings
    
    def embed_query(self, query: str) -> List[float]:
        """
        Generate embedding for a user query using Gemini

        Args:
            query: User query text

        Returns:
            Query embedding vector
        """
        logger.info(f"Generating embedding for query: {query[:100]}...")
        try:
            # Use query task type for user queries
            result = genai.embed_content(
                model=self.model,
                content=query,
                task_type="retrieval_query"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error generating query embedding: {str(e)}")
            # Fallback to document embedding
            return self.generate_embedding(query)

    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of embeddings for the current model

        Returns:
            Embedding dimension
        """
        # Gemini embedding dimensions
        model_dimensions = {
            "models/embedding-001": 768,
            "embedding-001": 768
        }

        return model_dimensions.get(self.model, 768)
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score (-1 to 1)
        """
        try:
            import numpy as np
            
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except ImportError:
            logger.warning("NumPy not available, using basic similarity calculation")
            # Basic dot product similarity (not normalized)
            return sum(a * b for a, b in zip(embedding1, embedding2))
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0
    
    def test_connection(self) -> bool:
        """
        Test the Gemini API connection

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            test_embedding = self.generate_embedding("test connection")
            logger.info("Gemini API connection test successful")
            return len(test_embedding) > 0
        except Exception as e:
            logger.error(f"Gemini API connection test failed: {str(e)}")
            return False

# Example usage and testing
if __name__ == "__main__":
    try:
        # Initialize embedding service
        embedding_service = TelecomEmbeddingService()
        
        # Test connection
        if not embedding_service.test_connection():
            print("Failed to connect to OpenAI API. Please check your API key.")
            exit(1)
        
        print(f"Gemini Embedding Service initialized successfully")
        print(f"Model: {embedding_service.model}")
        print(f"Embedding dimension: {embedding_service.get_embedding_dimension()}")

        # Test single embedding
        test_text = "How do I fix my internet connection?"
        embedding = embedding_service.generate_embedding(test_text)
        print(f"\nGenerated embedding for test query:")
        print(f"Text: {test_text}")
        print(f"Embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")

        # Test batch embeddings
        test_texts = [
            "Internet connection is slow",
            "Mobile phone has no signal",
            "Billing question about charges"
        ]

        batch_embeddings = embedding_service.generate_embeddings_batch(test_texts)
        print(f"\nGenerated {len(batch_embeddings)} embeddings in batch")

        # Test similarity
        if len(batch_embeddings) >= 2:
            similarity = embedding_service.calculate_similarity(
                batch_embeddings[0], batch_embeddings[1]
            )
            print(f"Similarity between first two embeddings: {similarity:.4f}")

    except Exception as e:
        print(f"Error: {str(e)}")
        print("Make sure to set your GOOGLE_API_KEY in the .env file")
