"""
TeleConnect Customer Support RAG Bot - Streamlit Interface
Main application file for the customer support chatbot
"""

import streamlit as st
from datetime import datetime
from typing import Dict
import logging

# Import our RAG pipeline
from rag_pipeline import TelecomRAGPipeline

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="TeleConnect Customer Support",
    page_icon="📞",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79, #2e86ab);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }

    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #2e86ab;
    }

    .user-message {
        background-color: #f0f8ff;
        border-left-color: #1f4e79;
    }

    .assistant-message {
        background-color: #f8f9fa;
        border-left-color: #2e86ab;
    }

    .sidebar-info {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 5px;
        margin: 0.5rem 0;
    }

    .followup-questions {
        background-color: #e8f4f8;
        padding: 1rem;
        border-radius: 5px;
        margin-top: 1rem;
    }

    .intent-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 0.25rem;
    }

    .intent-technical { background-color: #ffeaa7; color: #2d3436; }
    .intent-billing { background-color: #fab1a0; color: #2d3436; }
    .intent-general { background-color: #a8e6cf; color: #2d3436; }
    .intent-complaint { background-color: #ff7675; color: white; }
    .intent-emergency { background-color: #e17055; color: white; }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def initialize_rag_pipeline():
    """Initialize the RAG pipeline (cached for performance)"""
    try:
        return TelecomRAGPipeline()
    except Exception as e:
        st.error(f"Failed to initialize RAG pipeline: {str(e)}")
        st.error("Please check your API keys in the .env file")
        return None

def display_message(role: str, content: str, timestamp: str = None, metadata: Dict = None):
    """Display a chat message with proper styling"""
    if timestamp is None:
        timestamp = datetime.now().strftime("%H:%M:%S")

    css_class = "user-message" if role == "user" else "assistant-message"
    icon = "👤" if role == "user" else "🤖"

    st.markdown(f"""
    <div class="chat-message {css_class}">
        <strong>{icon} {role.title()}</strong> <small>({timestamp})</small><br>
        {content}
    </div>
    """, unsafe_allow_html=True)

    # Display metadata for assistant messages
    if role == "assistant" and metadata:
        intent = metadata.get("intent", {})
        if intent:
            category = intent.get("category", "general")
            urgency = intent.get("urgency", "medium")

            intent_class = f"intent-{category.replace('_', '-')}"
            st.markdown(f"""
            <div style="margin-top: 0.5rem;">
                <span class="intent-badge {intent_class}">
                    {category.replace('_', ' ').title()} - {urgency.title()} Priority
                </span>
            </div>
            """, unsafe_allow_html=True)

def display_followup_questions(questions: list, session_id: str):
    """Display follow-up questions as clickable buttons"""
    if questions:
        st.markdown('<div class="followup-questions">', unsafe_allow_html=True)
        st.markdown("**💡 Suggested follow-up questions:**")

        cols = st.columns(len(questions))
        for i, question in enumerate(questions):
            with cols[i]:
                if st.button(question, key=f"followup_{session_id}_{i}"):
                    st.session_state.followup_clicked = question

        st.markdown('</div>', unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>📞 TeleConnect Customer Support</h1>
        <p>AI-Powered Customer Support Assistant</p>
    </div>
    """, unsafe_allow_html=True)

    # Initialize RAG pipeline
    rag = initialize_rag_pipeline()
    if not rag:
        st.stop()

    # Initialize session state
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "session_id" not in st.session_state:
        st.session_state.session_id = None
    if "customer_info" not in st.session_state:
        st.session_state.customer_info = {}

    # Sidebar
    with st.sidebar:
        st.header("🔧 System Information")

        # System stats
        with st.expander("System Status", expanded=True):
            try:
                stats = rag.get_system_stats()

                # API Status
                gemini_embeddings_status = "✅" if stats.get("api_status", {}).get("gemini_embeddings") else "❌"
                gemini_llm_status = "✅" if stats.get("api_status", {}).get("gemini_llm") else "❌"

                st.markdown(f"""
                <div class="sidebar-info">
                    <strong>API Status:</strong><br>
                    Gemini Embeddings: {gemini_embeddings_status}<br>
                    Gemini LLM: {gemini_llm_status}<br><br>

                    <strong>Knowledge Base:</strong><br>
                    Documents: {stats.get('vector_database', {}).get('total_documents', 0)}<br><br>

                    <strong>Active Sessions:</strong><br>
                    {stats.get('conversation_memory', {}).get('active_sessions', 0)}
                </div>
                """, unsafe_allow_html=True)

            except Exception as e:
                st.error(f"Error loading system stats: {str(e)}")

        # Customer Information
        st.header("👤 Customer Information")
        with st.expander("Update Customer Info"):
            account_number = st.text_input("Account Number",
                                         value=st.session_state.customer_info.get("account_number", ""))
            plan_type = st.selectbox("Plan Type",
                                   ["", "Basic Mobile", "Standard Mobile", "Premium Mobile",
                                    "Basic Internet", "Standard Internet", "Premium Internet"],
                                   index=0)
            service_area = st.text_input("Service Area",
                                       value=st.session_state.customer_info.get("service_area", ""))

            if st.button("Update Info"):
                customer_info = {}
                if account_number:
                    customer_info["account_number"] = account_number
                if plan_type:
                    customer_info["plan_type"] = plan_type
                if service_area:
                    customer_info["service_area"] = service_area

                if customer_info and st.session_state.session_id:
                    rag.update_customer_info(st.session_state.session_id, customer_info)
                    st.session_state.customer_info.update(customer_info)
                    st.success("Customer information updated!")

        # Session Management
        st.header("💬 Session Management")
        if st.button("Start New Session"):
            st.session_state.messages = []
            st.session_state.session_id = None
            st.session_state.customer_info = {}
            st.rerun()

        if st.session_state.session_id:
            st.info(f"Session ID: {st.session_state.session_id[:8]}...")

            # Session info
            try:
                session_info = rag.get_session_info(st.session_state.session_id)
                st.markdown(f"""
                <div class="sidebar-info">
                    <strong>Session Summary:</strong><br>
                    Messages: {session_info.get('message_count', 0)}<br>
                    {session_info.get('conversation_summary', 'No summary available')}
                </div>
                """, unsafe_allow_html=True)
            except Exception as e:
                st.error(f"Error loading session info: {str(e)}")

    # Main chat interface
    st.header("💬 Chat with Support Assistant")

    # Display chat messages
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.messages:
            display_message(
                role=message["role"],
                content=message["content"],
                timestamp=message.get("timestamp"),
                metadata=message.get("metadata")
            )

    # Handle follow-up question clicks
    if hasattr(st.session_state, 'followup_clicked'):
        user_input = st.session_state.followup_clicked
        delattr(st.session_state, 'followup_clicked')
    else:
        # Chat input
        user_input = st.chat_input("Type your question here...")

    # Process user input
    if user_input:
        # Add user message to session state
        user_message = {
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        st.session_state.messages.append(user_message)

        # Display user message
        with chat_container:
            display_message(**user_message)

        # Process query through RAG pipeline
        with st.spinner("🤔 Thinking..."):
            try:
                result = rag.process_query(
                    query=user_input,
                    session_id=st.session_state.session_id,
                    user_id="streamlit_user"
                )

                # Update session ID if new
                if not st.session_state.session_id:
                    st.session_state.session_id = result["session_id"]

                # Add assistant response to session state
                assistant_message = {
                    "role": "assistant",
                    "content": result["response"],
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                    "metadata": {
                        "intent": result.get("intent"),
                        "context_sources": result.get("context_sources")
                    }
                }
                st.session_state.messages.append(assistant_message)

                # Display assistant response
                with chat_container:
                    display_message(**assistant_message)

                # Display follow-up questions
                followup_questions = result.get("followup_questions", [])
                if followup_questions:
                    display_followup_questions(followup_questions, st.session_state.session_id)

                # Show context sources in expander
                context_sources = result.get("context_sources", [])
                if context_sources:
                    with st.expander("📚 Information Sources"):
                        for source in context_sources:
                            relevance = source.get("relevance_score", 0)
                            section = source.get("section", "Unknown")
                            st.markdown(f"• **{section}** (Relevance: {relevance:.2f})")

            except Exception as e:
                st.error(f"Error processing query: {str(e)}")
                logger.error(f"Error in main chat processing: {str(e)}")

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.8rem;">
        TeleConnect Customer Support RAG Bot | Powered by Gemini AI (Embeddings & LLM)
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
