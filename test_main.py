"""
Simple test version of the main application
"""

import streamlit as st
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

st.set_page_config(
    page_title="TeleConnect Customer Support",
    page_icon="📞",
    layout="wide"
)

st.title("📞 TeleConnect Customer Support")
st.write("Testing basic Streamlit functionality...")

# Test basic functionality
if st.button("Test Button"):
    st.success("Button works!")

# Test importing our modules
try:
    st.write("Testing imports...")
    
    from embeddings import TelecomEmbeddingService
    st.success("✅ Embeddings module imported successfully")
    
    from gemini_llm import TelecomGeminiLLM
    st.success("✅ Gemini LLM module imported successfully")
    
    from vector_database import TelecomVectorDatabase
    st.success("✅ Vector database module imported successfully")
    
    from conversation_memory import TelecomConversationMemory
    st.success("✅ Conversation memory module imported successfully")
    
    from rag_pipeline import TelecomRAGPipeline
    st.success("✅ RAG pipeline module imported successfully")
    
    st.write("All modules imported successfully!")
    
    # Test basic initialization
    if st.button("Test RAG Pipeline Initialization"):
        with st.spinner("Initializing RAG pipeline..."):
            try:
                rag = TelecomRAGPipeline()
                st.success("✅ RAG pipeline initialized successfully!")
                
                # Test system stats
                stats = rag.get_system_stats()
                st.json(stats)
                
            except Exception as e:
                st.error(f"❌ Error initializing RAG pipeline: {str(e)}")
                st.exception(e)

except Exception as e:
    st.error(f"❌ Error importing modules: {str(e)}")
    st.exception(e)

st.write("---")
st.write("If you see this message, Streamlit is working correctly!")
