"""
Architecture Verification Script for TeleConnect RAG Bot
Verifies that the system uses OpenAI SDK for embeddings and only Gemini for LLM
"""

import os
import sys
from pathlib import Path

def check_openai_sdk_usage():
    """Verify OpenAI SDK is used for embeddings"""
    print("🔍 Checking OpenAI SDK Usage...")
    
    embeddings_file = Path("embeddings.py")
    if not embeddings_file.exists():
        print("❌ embeddings.py not found")
        return False
    
    with open(embeddings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for OpenAI SDK imports and usage
    checks = [
        ("import openai", "OpenAI library import"),
        ("openai.OpenAI(", "OpenAI SDK client initialization"),
        ("self.client.embeddings.create(", "OpenAI SDK embeddings API call"),
        ("response.data[0].embedding", "OpenAI SDK response parsing"),
        ("text-embedding-3-small", "OpenAI embedding model")
    ]
    
    all_passed = True
    for check, description in checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
            all_passed = False
    
    return all_passed

def check_gemini_only_llm():
    """Verify only Gemini is used as LLM"""
    print("\n🔍 Checking Gemini-Only LLM Usage...")
    
    # Check gemini_llm.py
    gemini_file = Path("gemini_llm.py")
    if not gemini_file.exists():
        print("❌ gemini_llm.py not found")
        return False
    
    with open(gemini_file, 'r', encoding='utf-8') as f:
        gemini_content = f.read()
    
    # Check for Gemini usage
    gemini_checks = [
        ("import google.generativeai as genai", "Google Generative AI import"),
        ("genai.configure(", "Gemini configuration"),
        ("genai.GenerativeModel(", "Gemini model initialization"),
        ("gemini-1.5-flash", "Gemini Flash 1.5 model"),
        ("model.generate_content(", "Gemini content generation")
    ]
    
    gemini_passed = True
    for check, description in gemini_checks:
        if check in gemini_content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
            gemini_passed = False
    
    # Check that OpenAI GPT is NOT used for LLM
    print("\n🔍 Verifying NO OpenAI GPT usage for LLM...")
    
    files_to_check = ["gemini_llm.py", "rag_pipeline.py", "main.py"]
    no_openai_gpt = True
    
    for file_name in files_to_check:
        file_path = Path(file_name)
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for OpenAI GPT usage (should NOT be present)
            gpt_indicators = [
                "gpt-3.5-turbo",
                "gpt-4",
                "ChatOpenAI",
                "openai.ChatCompletion",
                "openai.Completion"
            ]
            
            for indicator in gpt_indicators:
                if indicator in content:
                    print(f"❌ Found OpenAI GPT usage in {file_name}: {indicator}")
                    no_openai_gpt = False
    
    if no_openai_gpt:
        print("✅ Confirmed: NO OpenAI GPT usage found - Only Gemini for LLM")
    
    return gemini_passed and no_openai_gpt

def check_rag_pipeline_architecture():
    """Verify RAG pipeline architecture"""
    print("\n🔍 Checking RAG Pipeline Architecture...")
    
    rag_file = Path("rag_pipeline.py")
    if not rag_file.exists():
        print("❌ rag_pipeline.py not found")
        return False
    
    with open(rag_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check architecture components
    architecture_checks = [
        ("TelecomEmbeddingService", "OpenAI Embeddings Service"),
        ("TelecomVectorDatabase", "ChromaDB Vector Database"),
        ("TelecomGeminiLLM", "Gemini LLM Service"),
        ("TelecomConversationMemory", "Conversation Memory"),
        ("similarity_search", "Vector similarity search"),
        ("embed_query", "Query embedding"),
        ("generate_response", "LLM response generation")
    ]
    
    all_passed = True
    for check, description in architecture_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
            all_passed = False
    
    return all_passed

def check_requirements_file():
    """Check requirements.txt for correct dependencies"""
    print("\n🔍 Checking Requirements File...")
    
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    with open(req_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check required dependencies
    required_deps = [
        ("openai>=", "OpenAI SDK"),
        ("google-generativeai>=", "Google Generative AI"),
        ("chromadb>=", "ChromaDB"),
        ("streamlit>=", "Streamlit"),
        ("langchain>=", "LangChain"),
        ("python-dotenv>=", "Python Dotenv")
    ]
    
    all_passed = True
    for dep, description in required_deps:
        if dep in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
            all_passed = False
    
    return all_passed

def main():
    """Main verification function"""
    print("🔍 TeleConnect RAG Bot Architecture Verification")
    print("=" * 60)
    
    results = []
    
    # Run all checks
    results.append(("OpenAI SDK Usage", check_openai_sdk_usage()))
    results.append(("Gemini-Only LLM", check_gemini_only_llm()))
    results.append(("RAG Pipeline Architecture", check_rag_pipeline_architecture()))
    results.append(("Requirements File", check_requirements_file()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{check_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 Architecture Verification PASSED!")
        print("\n✅ Confirmed Architecture:")
        print("   • OpenAI SDK for embeddings")
        print("   • Gemini AI Flash 1.5 for LLM (ONLY)")
        print("   • ChromaDB for vector storage")
        print("   • Complete RAG pipeline")
        print("   • Streamlit web interface")
    else:
        print(f"\n⚠️ {total - passed} checks failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
