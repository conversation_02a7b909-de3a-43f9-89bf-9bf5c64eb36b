"""
ChromaDB Vector Database Implementation for TeleConnect Customer Support RAG Bot
Handles vector storage, retrieval, and similarity search
"""

import os
import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional
from langchain.docstore.document import Document
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelecomVectorDatabase:
    """
    ChromaDB vector database for storing and retrieving telecom support documents
    """
    
    def __init__(self, db_path: str = "./chroma_db", collection_name: str = "telecom_support"):
        """
        Initialize ChromaDB vector database
        
        Args:
            db_path: Path to store ChromaDB data
            collection_name: Name of the collection to store documents
        """
        self.db_path = db_path
        self.collection_name = collection_name
        
        # Create directory if it doesn't exist
        os.makedirs(db_path, exist_ok=True)
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=db_path,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Get or create collection
        try:
            self.collection = self.client.get_collection(name=collection_name)
            logger.info(f"Loaded existing collection: {collection_name}")
        except Exception:
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "TeleConnect customer support documents"}
            )
            logger.info(f"Created new collection: {collection_name}")
    
    def add_documents(self, documents: List[Document], embeddings: List[List[float]]) -> None:
        """
        Add documents with embeddings to the vector database
        
        Args:
            documents: List of Document objects
            embeddings: List of embedding vectors corresponding to documents
        """
        if len(documents) != len(embeddings):
            raise ValueError("Number of documents must match number of embeddings")
        
        # Prepare data for ChromaDB
        ids = []
        texts = []
        metadatas = []
        
        for i, doc in enumerate(documents):
            doc_id = f"doc_{i}_{hash(doc.page_content) % 1000000}"
            ids.append(doc_id)
            texts.append(doc.page_content)
            
            # Prepare metadata (ChromaDB requires string values)
            metadata = {}
            for key, value in doc.metadata.items():
                if isinstance(value, (str, int, float, bool)):
                    metadata[key] = str(value)
                else:
                    metadata[key] = json.dumps(value)
            metadatas.append(metadata)
        
        # Add to collection
        try:
            self.collection.add(
                ids=ids,
                documents=texts,
                embeddings=embeddings,
                metadatas=metadatas
            )
            logger.info(f"Added {len(documents)} documents to vector database")
        except Exception as e:
            logger.error(f"Error adding documents to database: {str(e)}")
            raise
    
    def similarity_search(self, query_embedding: List[float], k: int = 5, 
                         filter_metadata: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        Perform similarity search using query embedding
        
        Args:
            query_embedding: Query embedding vector
            k: Number of results to return
            filter_metadata: Optional metadata filters
            
        Returns:
            List of search results with documents and scores
        """
        try:
            # Prepare query parameters
            query_params = {
                "query_embeddings": [query_embedding],
                "n_results": k
            }
            
            # Add metadata filter if provided
            if filter_metadata:
                query_params["where"] = filter_metadata
            
            # Perform search
            results = self.collection.query(**query_params)
            
            # Format results
            formatted_results = []
            for i in range(len(results["ids"][0])):
                result = {
                    "id": results["ids"][0][i],
                    "document": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "distance": results["distances"][0][i] if "distances" in results else None
                }
                formatted_results.append(result)
            
            logger.info(f"Found {len(formatted_results)} similar documents")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error performing similarity search: {str(e)}")
            raise
    
    def search_by_text(self, query_text: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Search documents by text query (uses ChromaDB's built-in embedding)
        
        Args:
            query_text: Text query
            k: Number of results to return
            
        Returns:
            List of search results
        """
        try:
            results = self.collection.query(
                query_texts=[query_text],
                n_results=k
            )
            
            # Format results
            formatted_results = []
            for i in range(len(results["ids"][0])):
                result = {
                    "id": results["ids"][0][i],
                    "document": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "distance": results["distances"][0][i] if "distances" in results else None
                }
                formatted_results.append(result)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error performing text search: {str(e)}")
            raise
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the collection
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            count = self.collection.count()
            
            # Get sample of documents to analyze sections
            sample_results = self.collection.get(limit=min(100, count))
            sections = {}
            
            if sample_results["metadatas"]:
                for metadata in sample_results["metadatas"]:
                    section = metadata.get("section", "Unknown")
                    sections[section] = sections.get(section, 0) + 1
            
            return {
                "total_documents": count,
                "collection_name": self.collection_name,
                "sections_sample": sections
            }
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {str(e)}")
            return {"error": str(e)}
    
    def delete_collection(self) -> None:
        """
        Delete the entire collection
        """
        try:
            self.client.delete_collection(name=self.collection_name)
            logger.info(f"Deleted collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error deleting collection: {str(e)}")
            raise
    
    def reset_collection(self) -> None:
        """
        Reset the collection (delete and recreate)
        """
        try:
            self.delete_collection()
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "TeleConnect customer support documents"}
            )
            logger.info(f"Reset collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error resetting collection: {str(e)}")
            raise
    
    def filter_by_section(self, section: str, k: int = 10) -> List[Dict[str, Any]]:
        """
        Get documents from a specific section
        
        Args:
            section: Section name to filter by
            k: Maximum number of results
            
        Returns:
            List of documents from the specified section
        """
        try:
            results = self.collection.get(
                where={"section": section},
                limit=k
            )
            
            formatted_results = []
            for i in range(len(results["ids"])):
                result = {
                    "id": results["ids"][i],
                    "document": results["documents"][i],
                    "metadata": results["metadatas"][i]
                }
                formatted_results.append(result)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error filtering by section: {str(e)}")
            raise

# Example usage and testing
if __name__ == "__main__":
    # Initialize vector database
    vector_db = TelecomVectorDatabase()
    
    # Get collection statistics
    stats = vector_db.get_collection_stats()
    print("Vector Database Statistics:")
    print(f"Total documents: {stats.get('total_documents', 0)}")
    print(f"Collection name: {stats.get('collection_name', 'N/A')}")
    
    if stats.get('sections_sample'):
        print("\nSections in database:")
        for section, count in stats['sections_sample'].items():
            print(f"  {section}: {count} documents")
    
    # Test text search if database has documents
    if stats.get('total_documents', 0) > 0:
        print("\nTesting text search for 'internet connection':")
        results = vector_db.search_by_text("internet connection problems", k=3)
        for i, result in enumerate(results):
            print(f"\nResult {i+1}:")
            print(f"Section: {result['metadata'].get('section', 'N/A')}")
            print(f"Content: {result['document'][:200]}...")
    else:
        print("\nNo documents in database. Run the main application to populate it.")
