"""
Document Loader for TeleConnect Customer Support RAG Bot
Handles loading, processing, and chunking of support documents
"""

import os
from typing import List, Dict, Any
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.docstore.document import Document
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelecomDocumentLoader:
    """
    Document loader specifically designed for telecommunication support documents
    """
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Initialize the document loader
        
        Args:
            chunk_size: Size of each text chunk
            chunk_overlap: Overlap between chunks for context preservation
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )
        
    def load_document(self, file_path: str) -> str:
        """
        Load document content from file
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Document content as string
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            logger.info(f"Successfully loaded document: {file_path}")
            return content
        except FileNotFoundError:
            logger.error(f"Document not found: {file_path}")
            raise
        except Exception as e:
            logger.error(f"Error loading document {file_path}: {str(e)}")
            raise
    
    def preprocess_content(self, content: str) -> str:
        """
        Preprocess document content for better chunking
        
        Args:
            content: Raw document content
            
        Returns:
            Preprocessed content
        """
        # Remove excessive whitespace
        content = ' '.join(content.split())
        
        # Ensure proper spacing after headers
        content = content.replace('#', '\n#')
        
        # Ensure proper spacing for Q&A sections
        content = content.replace('**Q:', '\n\n**Q:')
        content = content.replace('**A:', '\n**A:')
        
        # Ensure proper spacing for issue sections
        content = content.replace('**Issue:', '\n\n**Issue:')
        
        return content.strip()
    
    def create_chunks(self, content: str) -> List[Document]:
        """
        Split content into chunks with metadata
        
        Args:
            content: Document content to chunk
            
        Returns:
            List of Document objects with chunks and metadata
        """
        # Preprocess content
        processed_content = self.preprocess_content(content)
        
        # Split into chunks
        chunks = self.text_splitter.split_text(processed_content)
        
        # Create Document objects with metadata
        documents = []
        for i, chunk in enumerate(chunks):
            # Extract section information for metadata
            section = self._extract_section_info(chunk)
            
            doc = Document(
                page_content=chunk,
                metadata={
                    "chunk_id": i,
                    "section": section,
                    "source": "telecom_support_data.txt",
                    "chunk_size": len(chunk),
                    "document_type": "customer_support"
                }
            )
            documents.append(doc)
        
        logger.info(f"Created {len(documents)} chunks from document")
        return documents
    
    def _extract_section_info(self, chunk: str) -> str:
        """
        Extract section information from chunk for metadata
        
        Args:
            chunk: Text chunk
            
        Returns:
            Section name or type
        """
        chunk_lower = chunk.lower()
        
        # Check for different section types
        if "internet connection" in chunk_lower or "slow internet" in chunk_lower:
            return "Internet Issues"
        elif "mobile service" in chunk_lower or "no signal" in chunk_lower:
            return "Mobile Issues"
        elif "billing" in chunk_lower or "payment" in chunk_lower:
            return "Billing Issues"
        elif "plan" in chunk_lower and ("mobile" in chunk_lower or "internet" in chunk_lower):
            return "Service Plans"
        elif "policy" in chunk_lower or "return" in chunk_lower:
            return "Policies"
        elif "**q:" in chunk_lower or "frequently asked" in chunk_lower:
            return "FAQ"
        elif "contact" in chunk_lower or "customer service" in chunk_lower:
            return "Contact Information"
        elif "equipment" in chunk_lower or "modem" in chunk_lower:
            return "Equipment"
        elif "coverage" in chunk_lower or "service area" in chunk_lower:
            return "Coverage"
        else:
            return "General Information"
    
    def load_and_process_documents(self, file_paths: List[str]) -> List[Document]:
        """
        Load and process multiple documents
        
        Args:
            file_paths: List of document file paths
            
        Returns:
            List of processed Document objects
        """
        all_documents = []
        
        for file_path in file_paths:
            try:
                content = self.load_document(file_path)
                chunks = self.create_chunks(content)
                all_documents.extend(chunks)
            except Exception as e:
                logger.error(f"Failed to process document {file_path}: {str(e)}")
                continue
        
        logger.info(f"Total documents processed: {len(all_documents)}")
        return all_documents
    
    def get_document_stats(self, documents: List[Document]) -> Dict[str, Any]:
        """
        Get statistics about processed documents
        
        Args:
            documents: List of Document objects
            
        Returns:
            Dictionary with document statistics
        """
        if not documents:
            return {"total_chunks": 0}
        
        sections = {}
        total_chars = 0
        
        for doc in documents:
            section = doc.metadata.get("section", "Unknown")
            sections[section] = sections.get(section, 0) + 1
            total_chars += len(doc.page_content)
        
        return {
            "total_chunks": len(documents),
            "sections": sections,
            "total_characters": total_chars,
            "average_chunk_size": total_chars / len(documents) if documents else 0
        }

# Example usage and testing
if __name__ == "__main__":
    # Initialize loader
    loader = TelecomDocumentLoader(chunk_size=1000, chunk_overlap=200)
    
    # Load and process the telecom support document
    try:
        documents = loader.load_and_process_documents(["telecom_support_data.txt"])
        
        # Print statistics
        stats = loader.get_document_stats(documents)
        print("Document Processing Statistics:")
        print(f"Total chunks: {stats['total_chunks']}")
        print(f"Total characters: {stats['total_characters']}")
        print(f"Average chunk size: {stats['average_chunk_size']:.2f}")
        print("\nSections found:")
        for section, count in stats['sections'].items():
            print(f"  {section}: {count} chunks")
        
        # Show first few chunks
        print("\nFirst 3 chunks:")
        for i, doc in enumerate(documents[:3]):
            print(f"\nChunk {i+1} (Section: {doc.metadata['section']}):")
            print(doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content)
            
    except Exception as e:
        print(f"Error: {str(e)}")
