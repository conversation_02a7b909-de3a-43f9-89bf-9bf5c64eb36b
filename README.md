# TeleConnect Customer Support RAG Bot

A comprehensive Retrieval-Augmented Generation (RAG) chatbot for telecommunications customer support, powered by OpenAI embeddings and Google's Gemini AI Flash 1.5.

## 🌟 Features

- **Advanced RAG Pipeline**: Query → Embedding → Retrieval → Context + Memory → LLM Response
- **OpenAI Embeddings**: High-quality text embeddings using OpenAI's latest models
- **Gemini AI Flash 1.5**: Fast and intelligent responses using Google's advanced LLM
- **ChromaDB Vector Database**: Efficient vector storage and similarity search
- **Conversation Memory**: Maintains context across multiple interactions
- **Document Processing**: Intelligent chunking and processing of support documents
- **Intent Classification**: Automatically categorizes customer queries
- **Streamlit UI**: User-friendly web interface
- **Session Management**: Persistent conversation sessions
- **Customer Information**: Track and update customer details

## 🏗️ Architecture

```
User Query → Embedding Service → Vector Database → Context Retrieval
                                                         ↓
Response ← Gemini AI LLM ← Context + Conversation Memory
```

### Components

1. **Document Loader** (`document_loader.py`): Processes and chunks support documents
2. **Embeddings Service** (`embeddings.py`): Generates embeddings using OpenAI
3. **Vector Database** (`vector_database.py`): ChromaDB for vector storage and retrieval
4. **Gemini LLM** (`gemini_llm.py`): Response generation using Gemini AI
5. **Conversation Memory** (`conversation_memory.py`): Session and context management
6. **RAG Pipeline** (`rag_pipeline.py`): Orchestrates all components
7. **Streamlit UI** (`main.py`): Web-based user interface

## 🚀 Quick Start

### Prerequisites

- Python 3.10 or higher
- OpenAI API key
- Google AI API key (for Gemini)

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd voice
   ```

2. **Install dependencies**:
   ```bash
   pip install openai google-generativeai chromadb langchain langchain-openai langchain-google-genai langchain-chroma streamlit python-dotenv
   ```

3. **Set up environment variables**:
   Create a `.env` file in the project root:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   GOOGLE_API_KEY=your_google_api_key_here
   CHROMA_DB_PATH=./chroma_db
   APP_NAME=TeleConnect Customer Support RAG Bot
   MAX_CONVERSATION_HISTORY=10
   CHUNK_SIZE=1000
   CHUNK_OVERLAP=200
   ```

4. **Test the system**:
   ```bash
   python test_system.py
   ```

5. **Run the application**:
   ```bash
   streamlit run main.py
   ```

## 📖 Usage

### Web Interface

1. Open your browser to `http://localhost:8501`
2. Start chatting with the support bot
3. Update customer information in the sidebar
4. View conversation history and system status

### Example Queries

- "My internet connection is very slow"
- "How do I change my mobile plan?"
- "What are the charges on my bill?"
- "I'm having trouble with my phone signal"
- "How do I set up international roaming?"

### API Usage

```python
from rag_pipeline import TelecomRAGPipeline

# Initialize the RAG pipeline
rag = TelecomRAGPipeline()

# Process a query
result = rag.process_query("My internet is slow")

print(result["response"])
print(result["intent"])
print(result["followup_questions"])
```

## 📁 Project Structure

```
voice/
├── main.py                     # Streamlit web interface
├── rag_pipeline.py            # Main RAG pipeline orchestrator
├── document_loader.py         # Document processing and chunking
├── embeddings.py              # OpenAI embeddings service
├── vector_database.py         # ChromaDB vector database
├── gemini_llm.py             # Gemini AI LLM integration
├── conversation_memory.py     # Conversation and session management
├── test_system.py            # Comprehensive test suite
├── telecom_support_data.txt  # Sample support knowledge base
├── .env                      # Environment variables
├── README.md                 # This file
└── pyproject.toml           # Project configuration
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_system.py
```

The test suite validates:
- Environment setup and API keys
- Document loading and processing
- OpenAI embeddings generation
- ChromaDB vector database operations
- Gemini AI LLM responses
- Conversation memory functionality
- Complete RAG pipeline integration

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key for embeddings | Required |
| `GOOGLE_API_KEY` | Google API key for Gemini AI | Required |
| `CHROMA_DB_PATH` | ChromaDB storage path | `./chroma_db` |
| `MAX_CONVERSATION_HISTORY` | Max messages in memory | `10` |
| `CHUNK_SIZE` | Document chunk size | `1000` |
| `CHUNK_OVERLAP` | Chunk overlap for context | `200` |

### Customization

1. **Add New Documents**: Place text files in the project directory and update the document loader
2. **Modify Chunking**: Adjust `CHUNK_SIZE` and `CHUNK_OVERLAP` in `.env`
3. **Change Models**: Update model names in the respective service classes
4. **Customize UI**: Modify `main.py` to change the Streamlit interface

## 📊 Knowledge Base

The system includes a comprehensive telecommunications knowledge base covering:

- **Internet Issues**: Connection problems, slow speeds, WiFi troubleshooting
- **Mobile Service**: Signal issues, data usage, roaming problems
- **Billing**: Payment issues, plan changes, unexpected charges
- **Service Plans**: Mobile, internet, and landline plan details
- **Equipment**: Modems, routers, mobile devices
- **Policies**: Return policies, service agreements, privacy
- **Contact Information**: Support numbers and channels

### Adding Real Data

Replace the content in `telecom_support_data.txt` with your actual support documentation. The system will automatically:
- Process and chunk the new content
- Generate embeddings
- Update the vector database
- Provide responses based on your real data

## 🔍 Features in Detail

### Intent Classification

The system automatically classifies queries into categories:
- `technical_support`: Technical issues
- `billing_inquiry`: Billing questions
- `service_change`: Plan modifications
- `general_info`: General inquiries
- `complaint`: Service complaints
- `emergency`: Urgent issues

### Conversation Memory

- Maintains conversation history across sessions
- Stores customer information
- Generates context summaries
- Supports session persistence

### Context Retrieval

- Semantic search using vector embeddings
- Relevance scoring
- Section-based filtering
- Multi-document support

## 🚨 Troubleshooting

### Common Issues

1. **API Key Errors**:
   - Ensure API keys are set correctly in `.env`
   - Check API key permissions and quotas

2. **Import Errors**:
   - Install all required dependencies
   - Check Python version (3.10+ required)

3. **Database Issues**:
   - Delete `./chroma_db` folder to reset
   - Check disk space and permissions

4. **Slow Performance**:
   - Reduce `CHUNK_SIZE` for faster processing
   - Limit conversation history length

### Getting Help

1. Run the test suite: `python test_system.py`
2. Check logs for detailed error messages
3. Verify all dependencies are installed
4. Ensure API keys have sufficient quotas

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for embeddings API
- Google for Gemini AI
- ChromaDB for vector database
- Streamlit for the web interface
- LangChain for RAG framework components

---

**TeleConnect Customer Support RAG Bot** - Revolutionizing customer support with AI-powered assistance.