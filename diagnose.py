"""
Diagnostic script to check system status
"""

import os
import sys
from dotenv import load_dotenv

load_dotenv()

def check_environment():
    """Check environment setup"""
    print("🔍 Environment Check")
    print("=" * 30)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check API key
    google_key = os.getenv("GOOGLE_API_KEY")
    if google_key and google_key != "your_google_api_key_here":
        print("✅ Google API key is set")
    else:
        print("❌ Google API key not set properly")
        return False
    
    return True

def test_imports():
    """Test all imports"""
    print("\n🔍 Import Check")
    print("=" * 30)
    
    modules = [
        ("streamlit", "Streamlit"),
        ("google.generativeai", "Google Generative AI"),
        ("chromadb", "ChromaDB"),
        ("langchain", "LangChain"),
        ("embeddings", "Custom Embeddings"),
        ("gemini_llm", "Custom Gemini LLM"),
        ("vector_database", "Custom Vector DB"),
        ("conversation_memory", "Custom Memory"),
        ("rag_pipeline", "Custom RAG Pipeline")
    ]
    
    all_good = True
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {str(e)}")
            all_good = False
    
    return all_good

def test_gemini_connection():
    """Test Gemini API connection"""
    print("\n🔍 Gemini API Check")
    print("=" * 30)
    
    try:
        from embeddings import TelecomEmbeddingService
        
        embedding_service = TelecomEmbeddingService()
        
        # Test connection
        if embedding_service.test_connection():
            print("✅ Gemini embeddings API working")
        else:
            print("❌ Gemini embeddings API failed")
            return False
        
        from gemini_llm import TelecomGeminiLLM
        
        llm = TelecomGeminiLLM()
        
        if llm.test_connection():
            print("✅ Gemini LLM API working")
        else:
            print("❌ Gemini LLM API failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini API test failed: {str(e)}")
        return False

def test_rag_pipeline():
    """Test RAG pipeline initialization"""
    print("\n🔍 RAG Pipeline Check")
    print("=" * 30)
    
    try:
        from rag_pipeline import TelecomRAGPipeline
        
        print("Initializing RAG pipeline...")
        rag = TelecomRAGPipeline()
        
        print("✅ RAG pipeline initialized")
        
        # Test system stats
        stats = rag.get_system_stats()
        print(f"✅ System stats retrieved")
        print(f"   Vector DB documents: {stats.get('vector_database', {}).get('total_documents', 0)}")
        print(f"   Active sessions: {stats.get('conversation_memory', {}).get('active_sessions', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG pipeline test failed: {str(e)}")
        return False

def test_simple_query():
    """Test a simple query"""
    print("\n🔍 Query Processing Check")
    print("=" * 30)
    
    try:
        from rag_pipeline import TelecomRAGPipeline
        
        rag = TelecomRAGPipeline()
        
        test_query = "Hello, can you help me?"
        print(f"Testing query: {test_query}")
        
        result = rag.process_query(test_query)
        
        if result and result.get("response"):
            print("✅ Query processing successful")
            print(f"   Response length: {len(result['response'])} characters")
            print(f"   Intent: {result.get('intent', {}).get('category', 'unknown')}")
            return True
        else:
            print("❌ Query processing failed - no response")
            return False
        
    except Exception as e:
        print(f"❌ Query processing test failed: {str(e)}")
        return False

def main():
    """Run all diagnostic tests"""
    print("🚀 TeleConnect RAG Bot Diagnostic")
    print("=" * 50)
    
    tests = [
        ("Environment", check_environment),
        ("Imports", test_imports),
        ("Gemini API", test_gemini_connection),
        ("RAG Pipeline", test_rag_pipeline),
        ("Query Processing", test_simple_query)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All diagnostics passed!")
        print("\nYour system should be working. Try:")
        print("python -m streamlit run main.py")
        print("\nThen open: http://localhost:8501")
    else:
        print(f"\n⚠️ {total - passed} tests failed.")
        print("Please fix the issues above before running the application.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
