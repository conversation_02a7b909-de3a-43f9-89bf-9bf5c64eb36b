"""
Complete RAG Pipeline for TeleConnect Customer Support Bot
Integrates document loading, embeddings, vector database, LLM, and conversation memory
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from dotenv import load_dotenv

# Import our custom modules
from document_loader import TelecomDocumentLoader
from embeddings import TelecomEmbeddingService
from vector_database import TelecomVectorDatabase
from gemini_llm import TelecomGeminiLLM
from conversation_memory import TelecomConversationMemory

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelecomRAGPipeline:
    """
    Complete RAG pipeline for TeleConnect customer support
    """
    
    def __init__(self, 
                 openai_api_key: Optional[str] = None,
                 google_api_key: Optional[str] = None,
                 db_path: str = "./chroma_db",
                 conversations_path: str = "./conversations"):
        """
        Initialize the RAG pipeline with all components
        
        Args:
            openai_api_key: OpenAI API key for embeddings
            google_api_key: Google API key for Gemini LLM
            db_path: Path for ChromaDB storage
            conversations_path: Path for conversation storage
        """
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        self.google_api_key = google_api_key or os.getenv("GOOGLE_API_KEY")
        
        # Initialize components
        logger.info("Initializing RAG pipeline components...")
        
        try:
            # Document loader
            self.document_loader = TelecomDocumentLoader(
                chunk_size=int(os.getenv("CHUNK_SIZE", 1000)),
                chunk_overlap=int(os.getenv("CHUNK_OVERLAP", 200))
            )
            
            # Embedding service
            self.embedding_service = TelecomEmbeddingService(api_key=self.openai_api_key)
            
            # Vector database
            self.vector_db = TelecomVectorDatabase(db_path=db_path)
            
            # LLM service
            self.llm = TelecomGeminiLLM(api_key=self.google_api_key)
            
            # Conversation memory
            self.memory = TelecomConversationMemory(
                storage_path=conversations_path,
                max_history=int(os.getenv("MAX_CONVERSATION_HISTORY", 10))
            )
            
            # Check if database needs to be populated
            stats = self.vector_db.get_collection_stats()
            if stats.get("total_documents", 0) == 0:
                logger.info("Vector database is empty, populating with documents...")
                self._populate_database()
            
            logger.info("RAG pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing RAG pipeline: {str(e)}")
            raise
    
    def _populate_database(self) -> None:
        """Populate the vector database with documents"""
        try:
            # Load and process documents
            documents = self.document_loader.load_and_process_documents([
                "telecom_support_data.txt"
            ])
            
            if not documents:
                logger.warning("No documents found to populate database")
                return
            
            # Generate embeddings
            logger.info("Generating embeddings for documents...")
            embeddings = self.embedding_service.embed_documents(documents)
            
            # Add to vector database
            logger.info("Adding documents to vector database...")
            self.vector_db.add_documents(documents, embeddings)
            
            # Log statistics
            stats = self.document_loader.get_document_stats(documents)
            logger.info(f"Populated database with {stats['total_chunks']} document chunks")
            
        except Exception as e:
            logger.error(f"Error populating database: {str(e)}")
            raise
    
    def process_query(self, query: str, session_id: Optional[str] = None, 
                     user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a user query through the complete RAG pipeline
        
        Args:
            query: User's question or query
            session_id: Optional session ID for conversation continuity
            user_id: Optional user ID
            
        Returns:
            Dictionary with response and metadata
        """
        try:
            # Create session if not provided
            if not session_id:
                session_id = self.memory.create_session(user_id=user_id)
            
            # Add user message to conversation history
            self.memory.add_message(session_id, "user", query)
            
            # Classify query intent
            intent_info = self.llm.classify_query_intent(query)
            
            # Generate query embedding
            logger.info(f"Processing query: {query[:50]}...")
            query_embedding = self.embedding_service.embed_query(query)
            
            # Retrieve relevant context
            context_results = self.vector_db.similarity_search(
                query_embedding=query_embedding,
                k=5  # Top 5 most relevant documents
            )
            
            # Get conversation history
            conversation_history = self.memory.get_conversation_history(
                session_id, limit=10
            )
            
            # Generate response using LLM
            response = self.llm.generate_response(
                query=query,
                context=context_results,
                conversation_history=conversation_history
            )
            
            # Add assistant response to conversation history
            self.memory.add_message(
                session_id, 
                "assistant", 
                response,
                metadata={
                    "intent": intent_info,
                    "context_sources": [ctx.get("metadata", {}).get("section") for ctx in context_results]
                }
            )
            
            # Generate follow-up questions
            followup_questions = self.llm.generate_followup_questions(query, response)
            
            # Prepare response
            result = {
                "response": response,
                "session_id": session_id,
                "intent": intent_info,
                "context_sources": [
                    {
                        "section": ctx.get("metadata", {}).get("section", "Unknown"),
                        "relevance_score": 1 - ctx.get("distance", 0)  # Convert distance to similarity
                    }
                    for ctx in context_results
                ],
                "followup_questions": followup_questions,
                "conversation_summary": self.memory.generate_context_summary(session_id)
            }
            
            logger.info(f"Successfully processed query for session {session_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            
            # Return error response
            error_response = {
                "response": "I apologize, but I'm experiencing technical difficulties. Please contact our customer service team at 1-800-TELECON for immediate assistance.",
                "session_id": session_id or self.memory.create_session(user_id=user_id),
                "error": str(e),
                "intent": {"category": "error", "urgency": "high", "requires_human": True},
                "context_sources": [],
                "followup_questions": ["Would you like me to connect you with a human agent?"],
                "conversation_summary": "Error occurred during processing"
            }
            
            return error_response
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """
        Get information about a conversation session
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session information dictionary
        """
        try:
            history = self.memory.get_conversation_history(session_id)
            customer_info = self.memory.get_customer_info(session_id)
            summary = self.memory.generate_context_summary(session_id)
            
            return {
                "session_id": session_id,
                "message_count": len(history),
                "customer_info": customer_info,
                "conversation_summary": summary,
                "last_messages": history[-5:] if history else []  # Last 5 messages
            }
            
        except Exception as e:
            logger.error(f"Error getting session info: {str(e)}")
            return {"error": str(e)}
    
    def update_customer_info(self, session_id: str, customer_info: Dict[str, Any]) -> None:
        """
        Update customer information for a session
        
        Args:
            session_id: Session identifier
            customer_info: Customer information to update
        """
        self.memory.update_customer_info(session_id, customer_info)
    
    def search_knowledge_base(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Search the knowledge base directly without conversation context
        
        Args:
            query: Search query
            k: Number of results to return
            
        Returns:
            List of search results
        """
        try:
            query_embedding = self.embedding_service.embed_query(query)
            results = self.vector_db.similarity_search(query_embedding, k=k)
            
            return [
                {
                    "content": result.get("document", ""),
                    "section": result.get("metadata", {}).get("section", "Unknown"),
                    "relevance_score": 1 - result.get("distance", 0)
                }
                for result in results
            ]
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {str(e)}")
            return []
    
    def get_system_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the RAG system
        
        Returns:
            System statistics dictionary
        """
        try:
            # Vector database stats
            db_stats = self.vector_db.get_collection_stats()
            
            # Conversation memory stats
            memory_stats = self.memory.get_session_stats()
            
            # Test API connections
            openai_status = self.embedding_service.test_connection()
            gemini_status = self.llm.test_connection()
            
            return {
                "vector_database": db_stats,
                "conversation_memory": memory_stats,
                "api_status": {
                    "openai_embeddings": openai_status,
                    "gemini_llm": gemini_status
                },
                "embedding_model": self.embedding_service.model,
                "llm_model": self.llm.model_name
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {str(e)}")
            return {"error": str(e)}
    
    def cleanup_old_data(self, days_old: int = 30) -> Dict[str, int]:
        """
        Clean up old conversation data
        
        Args:
            days_old: Number of days after which to delete data
            
        Returns:
            Cleanup statistics
        """
        try:
            cleaned_sessions = self.memory.cleanup_old_sessions(days_old)
            
            return {
                "cleaned_sessions": cleaned_sessions,
                "days_threshold": days_old
            }
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
            return {"error": str(e)}

# Example usage and testing
if __name__ == "__main__":
    try:
        # Initialize RAG pipeline
        rag = TelecomRAGPipeline()
        
        # Get system statistics
        stats = rag.get_system_stats()
        print("RAG Pipeline System Statistics:")
        print(f"Vector DB documents: {stats.get('vector_database', {}).get('total_documents', 0)}")
        print(f"Active sessions: {stats.get('conversation_memory', {}).get('active_sessions', 0)}")
        print(f"OpenAI API: {'✓' if stats.get('api_status', {}).get('openai_embeddings') else '✗'}")
        print(f"Gemini API: {'✓' if stats.get('api_status', {}).get('gemini_llm') else '✗'}")
        
        # Test query processing
        test_query = "My internet connection is very slow, what can I do?"
        print(f"\nTesting query: {test_query}")
        
        result = rag.process_query(test_query)
        
        print(f"\nResponse: {result['response']}")
        print(f"Intent: {result['intent']['category']} (urgency: {result['intent']['urgency']})")
        print(f"Session ID: {result['session_id']}")
        
        if result['followup_questions']:
            print("\nSuggested follow-up questions:")
            for i, question in enumerate(result['followup_questions'], 1):
                print(f"{i}. {question}")
        
        print("\nRAG pipeline test completed successfully!")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        print("Make sure to set your API keys in the .env file")
