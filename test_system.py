"""
Comprehensive Test Suite for TeleConnect Customer Support RAG Bot
Tests all components and their integration
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment():
    """Test environment setup and API keys"""
    print("🔧 Testing Environment Setup...")
    
    # Check API keys
    openai_key = os.getenv("OPENAI_API_KEY")
    google_key = os.getenv("GOOGLE_API_KEY")
    
    if not openai_key or openai_key == "your_openai_api_key_here":
        print("❌ OpenAI API key not set properly")
        return False
    else:
        print("✅ OpenAI API key found")
    
    if not google_key or google_key == "your_google_api_key_here":
        print("❌ Google API key not set properly")
        return False
    else:
        print("✅ Google API key found")
    
    # Check required files
    required_files = [
        "telecom_support_data.txt",
        "document_loader.py",
        "embeddings.py",
        "vector_database.py",
        "gemini_llm.py",
        "conversation_memory.py",
        "rag_pipeline.py",
        "main.py"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} found")
        else:
            print(f"❌ {file} missing")
            return False
    
    return True

def test_document_loader():
    """Test document loading and processing"""
    print("\n📄 Testing Document Loader...")
    
    try:
        from document_loader import TelecomDocumentLoader
        
        loader = TelecomDocumentLoader()
        documents = loader.load_and_process_documents(["telecom_support_data.txt"])
        
        if documents:
            print(f"✅ Loaded {len(documents)} document chunks")
            
            # Test document statistics
            stats = loader.get_document_stats(documents)
            print(f"✅ Document stats: {stats['total_chunks']} chunks, {len(stats['sections'])} sections")
            
            return True
        else:
            print("❌ No documents loaded")
            return False
            
    except Exception as e:
        print(f"❌ Document loader test failed: {str(e)}")
        return False

def test_embeddings():
    """Test OpenAI embeddings service"""
    print("\n🔗 Testing OpenAI Embeddings...")
    
    try:
        from embeddings import TelecomEmbeddingService
        
        embedding_service = TelecomEmbeddingService()
        
        # Test connection
        if not embedding_service.test_connection():
            print("❌ OpenAI API connection failed")
            return False
        
        print("✅ OpenAI API connection successful")
        
        # Test single embedding
        test_text = "How do I fix my internet connection?"
        embedding = embedding_service.generate_embedding(test_text)
        
        if embedding and len(embedding) > 0:
            print(f"✅ Generated embedding with dimension {len(embedding)}")
            return True
        else:
            print("❌ Failed to generate embedding")
            return False
            
    except Exception as e:
        print(f"❌ Embeddings test failed: {str(e)}")
        return False

def test_vector_database():
    """Test ChromaDB vector database"""
    print("\n🗄️ Testing Vector Database...")
    
    try:
        from vector_database import TelecomVectorDatabase
        from document_loader import TelecomDocumentLoader
        from embeddings import TelecomEmbeddingService
        
        # Initialize components
        vector_db = TelecomVectorDatabase(db_path="./test_chroma_db")
        loader = TelecomDocumentLoader()
        embedding_service = TelecomEmbeddingService()
        
        # Reset collection for clean test
        vector_db.reset_collection()
        
        # Load test documents
        documents = loader.load_and_process_documents(["telecom_support_data.txt"])
        if not documents:
            print("❌ No documents to test with")
            return False
        
        # Generate embeddings
        embeddings = embedding_service.embed_documents(documents[:5])  # Test with first 5 docs
        
        # Add to database
        vector_db.add_documents(documents[:5], embeddings)
        
        # Test search
        query_embedding = embedding_service.generate_embedding("internet connection problems")
        results = vector_db.similarity_search(query_embedding, k=3)
        
        if results:
            print(f"✅ Vector search returned {len(results)} results")
            return True
        else:
            print("❌ Vector search returned no results")
            return False
            
    except Exception as e:
        print(f"❌ Vector database test failed: {str(e)}")
        return False

def test_gemini_llm():
    """Test Gemini AI LLM service"""
    print("\n🤖 Testing Gemini AI LLM...")
    
    try:
        from gemini_llm import TelecomGeminiLLM
        
        llm = TelecomGeminiLLM()
        
        # Test connection
        if not llm.test_connection():
            print("❌ Gemini AI API connection failed")
            return False
        
        print("✅ Gemini AI API connection successful")
        
        # Test response generation
        test_query = "My internet is slow"
        mock_context = [
            {
                "document": "To fix slow internet: restart your modem and router",
                "metadata": {"section": "Internet Issues"}
            }
        ]
        
        response = llm.generate_response(test_query, mock_context)
        
        if response and len(response) > 10:
            print("✅ Generated response from Gemini AI")
            return True
        else:
            print("❌ Failed to generate response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini LLM test failed: {str(e)}")
        return False

def test_conversation_memory():
    """Test conversation memory system"""
    print("\n💭 Testing Conversation Memory...")
    
    try:
        from conversation_memory import TelecomConversationMemory
        
        memory = TelecomConversationMemory(storage_path="./test_conversations")
        
        # Create test session
        session_id = memory.create_session(user_id="test_user")
        
        # Add test messages
        memory.add_message(session_id, "user", "My internet is slow")
        memory.add_message(session_id, "assistant", "I can help you with that")
        
        # Test history retrieval
        history = memory.get_conversation_history(session_id)
        
        if len(history) == 2:
            print("✅ Conversation memory working correctly")
            return True
        else:
            print(f"❌ Expected 2 messages, got {len(history)}")
            return False
            
    except Exception as e:
        print(f"❌ Conversation memory test failed: {str(e)}")
        return False

def test_rag_pipeline():
    """Test complete RAG pipeline integration"""
    print("\n🔄 Testing Complete RAG Pipeline...")
    
    try:
        from rag_pipeline import TelecomRAGPipeline
        
        # Initialize RAG pipeline
        rag = TelecomRAGPipeline(
            db_path="./test_chroma_db",
            conversations_path="./test_conversations"
        )
        
        # Test system stats
        stats = rag.get_system_stats()
        print(f"✅ System stats retrieved: {stats.get('vector_database', {}).get('total_documents', 0)} documents")
        
        # Test query processing
        test_query = "How do I fix slow internet?"
        result = rag.process_query(test_query)
        
        if result and result.get("response"):
            print("✅ RAG pipeline query processing successful")
            print(f"   Response length: {len(result['response'])} characters")
            print(f"   Intent: {result.get('intent', {}).get('category', 'unknown')}")
            print(f"   Context sources: {len(result.get('context_sources', []))}")
            return True
        else:
            print("❌ RAG pipeline query processing failed")
            return False
            
    except Exception as e:
        print(f"❌ RAG pipeline test failed: {str(e)}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    
    import shutil
    
    test_dirs = ["./test_chroma_db", "./test_conversations"]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
                print(f"✅ Cleaned up {test_dir}")
            except Exception as e:
                print(f"⚠️ Could not clean up {test_dir}: {str(e)}")

def main():
    """Run all tests"""
    print("🚀 Starting TeleConnect RAG Bot System Tests\n")
    
    tests = [
        ("Environment Setup", test_environment),
        ("Document Loader", test_document_loader),
        ("OpenAI Embeddings", test_embeddings),
        ("Vector Database", test_vector_database),
        ("Gemini AI LLM", test_gemini_llm),
        ("Conversation Memory", test_conversation_memory),
        ("RAG Pipeline Integration", test_rag_pipeline)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Clean up test data
    cleanup_test_data()
    
    # Print summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your RAG bot is ready to use.")
        print("\nTo start the application, run:")
        print("streamlit run main.py")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
        print("Make sure your API keys are set correctly in the .env file.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
