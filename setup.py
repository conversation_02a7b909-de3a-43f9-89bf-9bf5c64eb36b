"""
Setup script for TeleConnect Customer Support RAG Bot
Installs dependencies and sets up the environment
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages from requirements.txt"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def setup_environment():
    """Set up environment file if it doesn't exist"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("🔧 Creating .env file...")
        env_content = """# API Keys - Replace with your actual API keys
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# ChromaDB Configuration
CHROMA_DB_PATH=./chroma_db

# Application Configuration
APP_NAME=TeleConnect Customer Support RAG Bot
MAX_CONVERSATION_HISTORY=10
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ .env file created!")
        print("⚠️  Please update the API keys in .env file before running the application")
    else:
        print("✅ .env file already exists")

def create_directories():
    """Create necessary directories"""
    directories = ["chroma_db", "conversations"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 10):
        print("❌ Python 3.10 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    else:
        print(f"✅ Python version: {sys.version}")
        return True

def main():
    """Main setup function"""
    print("🚀 Setting up TeleConnect Customer Support RAG Bot")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Create directories
    create_directories()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Update your API keys in the .env file:")
    print("   - OPENAI_API_KEY=your_actual_openai_key")
    print("   - GOOGLE_API_KEY=your_actual_google_key")
    print("\n2. Test the system:")
    print("   python test_system.py")
    print("\n3. Run the application:")
    print("   streamlit run main.py")
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
